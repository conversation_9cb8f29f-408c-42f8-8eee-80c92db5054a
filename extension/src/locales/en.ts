export const enMessages = {
  // Basic Extension Info
  extensionName: 'Mysta',
  extensionDescription: 'Mysta - Let AI Run the Web for You.',

  // Navigation and UI
  viewProfile: 'View Profile',
  conversations: 'Conversations',
  remoteConversation: 'Remote Conversation',
  miniapp: 'Applications',
  newChat: 'New chat',
  allChats: 'All Chats',
  user: 'User',
  language: 'Language',
  english: 'English',
  chinese: '中文',
  settings: 'Settings',
  logout: 'Logout',
  workflowDetail: 'Task Detail',
  confirm: 'Confirm',
  yes: 'Yes',
  no: 'No',
  back: 'Back',

  // Actions
  save: 'Save',
  cancel: 'Cancel',
  delete: 'Delete',
  close: 'Close',
  ok: 'OK',
  notNow: 'Not now',
  getStarted: 'Get started',

  // API Key Management
  setApiKey: 'Set API Key',
  enterApiKey: 'Enter $1 API Key',
  apiKeyDisabled: 'API key is disabled',
  goToGetApiKey: 'Go to get API Key',
  enableApiKey: 'Enable Your Mysta API Key',
  apiKeyRequired: 'You need to login to get API Key to use the full functionality',

  // Authentication
  signInWithMysta: 'Sign In with Mysta Web',
  mystaAccountDetected: 'Mysta Account Detected',

  // Chat Interface
  thinking: 'Thinking',
  typeMessage: 'Type a message...',
  send: 'Send',
  attachFile: 'Attach file',
  recordAudio: 'Record audio',
  workflowMode: 'Task Mode',
  normalMode: 'Chat Mode',

  // Error Messages
  insufficientCredits: 'Insufficient credits. Please add more credits to your account.',
  insufficientCreditsTip: 'Insufficient credits',
  rechargeTip: 'Add credits',
  errorOccurred: 'An error occurred. Please try again.',

  // TopBanner
  joinTelegramGroup: '🎉 Join our telegram group to get $5 credits!',
  cannotSaveForCurrentSite: 'Cannot save for the current site.',
  noMessagesToSave: 'No messages to save.',
  failedToSaveToSiteMemory: 'Failed to save to site memory.',

  // Success Messages
  savedToSiteMemorySuccessfully: 'Saved to site memory successfully!',

  // Tool Execution Status
  executing: 'Executing',
  executed: 'Executed',
  running: 'Running',
  error: 'Error',

  // Home Page
  letAiRunTheWeb: 'Let AI Run the Web for You',
  askAnything: 'Ask anything.\nAutomate Everything.',
  startTyping: 'Your AI agent is here to help.',
  privacyDisclaimer:
    'Mysta AI assistant may produce inaccurate information. Your data is kept private.',

  // Conversation Management
  deleteConversation: 'Delete Conversation',
  deleteConversationConfirm:
    'Are you sure you want to delete this conversation? This action cannot be undone.',
  newChatItem: 'New Chat',
  renameConversation: 'Rename Conversation',
  enterNewName: 'Enter new name',

  // Site Memory
  saveToSiteMemory: 'Save to site memory',
  saveToSiteMemoryTitle: 'Save to Site Memory',

  // Tooltips
  tooltipNewChat: 'New chat',
  tooltipClose: 'Close',
  tooltipDelete: 'Delete',
  tooltipConversations: 'Conversations',
  tooltipUser: 'User',
  tooltipSaveToSiteMemory: 'Save to site memory',
  tooltipRename: 'Rename',

  // Prompt Templates
  summarizeElonTweet: "Summarize Elon Musk's latest tweet.",
  postTweet: "Post a tweet saying: 'mysta is awesome'.",
  searchLinkedIn: 'Search for MystaAI on my LinkedIn.',

  // Copy Functionality
  copyToClipboard: 'Copy to clipboard',
  copied: 'Copied!',

  // File Upload
  selectFile: 'Select file',
  uploadFile: 'Upload file',

  // Status
  idle: 'Idle',
  waiting: 'Waiting',
  processing: 'Processing',

  // Common Actions
  edit: 'Edit',
  share: 'Share',
  export: 'Export',
  import: 'Import',
  refresh: 'Refresh',
  retry: 'Retry',

  // Validation Messages
  fieldRequired: 'This field is required',
  invalidEmail: 'Please enter a valid email address',
  invalidUrl: 'Please enter a valid URL',

  // Loading States
  loading: 'Loading...',
  pleaseWait: 'Please wait...',

  // Search
  search: 'Search',
  searchPlaceholder: 'Search conversations...',
  noResults: 'No results found',

  // Time/Date
  now: 'Now',
  today: 'Today',
  yesterday: 'Yesterday',
  thisWeek: 'This week',
  lastWeek: 'Last week',

  // Permissions
  permissionDenied: 'Permission denied',
  accessRestricted: 'Access restricted',

  // Connection
  connectionError: 'Connection error',
  networkError: 'Network error',
  retryConnection: 'Retry connection',

  // Features
  comingSoon: 'Coming soon',
  betaFeature: 'Beta feature',
  experimental: 'Experimental',

  // Reasoning
  thoughtProcess: 'Thought Process',

  // Model Settings
  setApiKeyTooltip: 'Set API Key',
  modelName: 'Mysta',

  // Telegram Configuration
  configureTelegramBot: 'Configure Telegram Bot',
  botToken: 'Bot Token',
  groupChatId: 'Group Chat ID',
  botTokenRequired: 'Bot token is required',
  invalidBotTokenFormat: 'Invalid bot token format',
  groupChatIdRequired: 'Group chat ID is required',
  invalidGroupChatIdFormat: 'Invalid group chat ID format',
  getBotTokenFromBotFather: 'Get this from @BotFather on Telegram',
  getChatIdFromUserInfoBot: 'Add @userinfobot to your group to get the chat ID',
  createRemoteConversation: 'Create Remote Conversation',
  creating: 'Creating...',

  // Remote Conversation
  remote: 'Remote',
  active: 'Active',
  inactive: 'Inactive',
  exitRemoteMode: 'Exit Remote Mode',
  exitRemoteModeConfirm: 'You are about to exit the remote mode. Please confirm again.',
  logoutTelegram: 'Logout Telegram',
  logoutTelegramConfirm: 'You are about to logout from Telegram. Please confirm again.',
  switchBot: 'Switch Bot',
  login: 'Log In',
  loginTip1: 'Open Telegram on your phone',
  loginTip2: 'Go to Settings > Devices > Link Desktop Device',
  loginTip3: 'Point your phone at this screen to confirm login',
  bindBotManually: 'Bind Bot Manually',
  enterPassword: 'Enter Password',
  passwordTip1:
    'You have Two-Step Verification enabled, so your account is protected with an additional password.',
  hint: 'Hint',
  next: 'Next',
  bindTelegramBot: 'Bind Telegram Bot',
  getBotFromBotFather: 'Get from @BotFather on Telegram',
  botFatherChatNotFound: 'BotFather chat not found',
  botListNotFound: 'Bot list not found',
  botFatherReplyButtonNotFound: 'BotFather reply button not found',
  apiTokenButtonNotFound: 'API Token button not found',
  bindGroupChat: 'Bind Group Chat',
  startBot: 'Start Bot',
  startBotWithGroup: 'Start Bot with Group',
  addBotToGroup: 'Add $1 to your group and set as admin first',
  botIsNotAnAdminOfTheGroup: 'Bot is not an admin of the group',
  botIsNotAMemberOfTheGroup: 'Bot is not a member of the group',
  noMessagesReceivedYet: 'No messages received yet. Send a message to @$1 to see it.',
  cannotStartBotWithTime: 'We cannot start you bot. You will have to wait $1',
  cannotStartBot: 'We cannot start you bot. Please try again later.',
  retryWithTime: 'Retry in $1',

  // MiniApps
  archived: 'Archived',
  newProject: 'New Project',
  create: 'Create',
  createProjectTitle: 'Create Project',
  projectNameLabel: 'Project name',
  pleaseEnterPlaceholder: 'Please enter...',
  deleteMiniappTitle: 'Delete MiniAPP',
  deleteMiniappContent:
    'This will permanently delete the MiniAPP and its conversation. This action cannot be undone.',
  filterAll: 'All',
  filterDeployed: 'Deployed',
  filterDeveloping: 'Developing',
  miniappStatusDeployed: 'Deployed',
  miniappStatusDeveloping: 'Developing',
  archive: 'Archive',
  activate: 'Activate',
  archivingTitle: 'Archiving',
  archiveConfirm1: 'You are about to archive the MiniAPP.',
  archiveConfirm2: 'After archiving, it needs to be activated',
  archiveConfirm3: 'before you can use it.',
  emptyMiniapps: 'You currently do not have any MiniApps.',
  emptyArchivedMiniapps: 'You currently do not have any archived MiniApps.',

  // MiniApp Detail
  miniappDetailHeaderDevelop: 'Development',
  miniappDetailHeaderHistory: 'Historical Versions',
  miniappDetailHello: 'Hello,',
  miniappDetailImAssistant: "I'm the MiniApp Assistant",
  miniappDetailTellMe: 'Tell me what script you want to create',
  miniappDetailNotFound: 'MiniApp not found',
  miniappDetailVersion: 'Version $1',
  miniappDetailInstalledOn: 'Installed on $1',
  miniappDetailGeneratedOn: 'Generated on $1',
  miniappDetailViewAllVersions: 'Historical versions',
} as const;
